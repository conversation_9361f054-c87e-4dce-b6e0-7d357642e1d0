<template>
  <ConfigProvider color-mode="platformLight">
    <Loading v-if="loading" />
    <div class="dish-create">
      <NavBar
        :title="isEdit ? '编辑菜品' : '添加菜品'"
        :container-style="{
          backgroundColor: '#F5F5F5',
          background: '#F5F5F5'
        }"
      >
        <template #left>
          <Icon icon-name="BackCenterB" :fill="themeColor.Title" style="margin-left: 4px;" @click="handleBackClick" />
        </template>
      </NavBar>

      <div class="form-container">
        <Form
          ref="formRef"
          :data="formData"
          :rules="rules"
          :align="FormType.ALIGN.RIGHT"
          :show-submit="false"
          @submit="onSubmit"
          @error="onFormError"
        >
          <!-- 菜品名称 -->
          <FormItem
            label="菜品名称"
            name="dishName"
            :value="formData.dishName"
            required
            @validate="onValidateDishName"
            class="form-item"
          >
            <TextField
              v-model="formData.dishName"
              placeholder="请输入菜品名称"
              :maxlength="15"
              clearable
              input-align="right"
              :is-form-item="true"
              style="padding: 0 !important;"
            />
          </FormItem>

          <!-- 菜品价格 -->
          <FormItem
            label="菜品价格"
            name="dishPrice"
            :value="formData.priceItem.dishPrice"
            required
            class="form-item"
          >
            <div class="price-input-wrapper">
              <TextField
                v-model="formData.priceItem.dishPrice"
                placeholder="请输入价格"
                type="number"
                input-align="right"
                :is-form-item="true"
                style="padding: 0 !important;"
              />
              <PriceUnitSelect v-model="formData.priceItem.priceType" />
            </div>
          </FormItem>

          <!-- 菜品图 -->
          <FormItem
            label="菜品图"
            name="image"
            :value="formData.dishResourceList"
            class="form-item"
            :layout="FormType.LAYOUT.VERTICAL"
          >
            <div class="image-upload-container">
              <Upload
                v-model="formData.dishResourceList"
                :max-count="1"
                :max-size="4 * 1024 * 1024"
                :is-preview="false"
                :prohibit-operation="false"
              />
              <div class="upload-tips">建议使用高清优质图，图片不得超过4M，建议分辨率为900x900，图片比例为1:1</div>
            </div>
          </FormItem>

          <!-- 是否添加为招牌菜 -->
          <FormItem
            name="specialty"
            :value="formData.specialty"
            class="form-item"
          >
            <template #label>
              <div class="form-item-label-isSpecialty">
                <div class="form-item-label-isSpecialty-text">是否添加为招牌菜</div>
                <OnixIcon icon="info" size="16" @click="handleInfoClick" />
              </div>
            </template>
            <Switch
              v-model="formData.specialty"
              :disabled="!canSetSpecialty"
              size="24"
              :is-form-item="true"
              @change="onSpecialtyChange"
            />
          </FormItem>
        </Form>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-button">
        <Button
          type="primary"
          block
          :style="{
            borderRadius: '8px'
          }"
          size="large"
          :disabled="!isFormValid"
          :loading="submitting"
          @click="onSubmit"
        >
          {{ isEdit ? '保存修改' : '确认添加' }}
        </Button>
      </div>
      <AlertMethod ref="alertMethodRef" />
    </div>
  </ConfigProvider>

</template>

<script lang="ts" setup>
  import {
    ref, computed, onMounted, onBeforeUnmount, reactive
  } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useStore } from 'vuex'
  import { invoke } from '@xhs/ozone-schema'
  import {
    Button,
    TextField,
    Switch,
    showToast,
    Form,
    FormItem,
    FormType,
    Icon,
    useThemeColor,
    ConfigProvider
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import Upload from './components/ImageUpload/index.vue'
  import NavBar from '~/components-next/NavBar.vue'
  import { postUpsertMenuDish } from '~/services/edith_post_upsert_menu_dish'
  import PriceUnitSelect from './components/PriceUnitSelect.vue'
  import AlertMethod from './components/AlertMethod.vue'
  import Loading from '~/components/loading-next/index.vue'
  import {
    IDishList,
    SpecialtyType,
  } from '~/types/menu'
  import '~/assets/svg/info.svg'

  const router = useRouter()
  const route = useRoute()
  const store = useStore()
  const submitting = ref(false)
  const formRef = ref()
  const alertMethodRef = ref()
  const loading = ref(false)
  const themeColor = useThemeColor()
  // 从路由获取参数
  const groupId = computed(() => route.query.groupId as string)
  const poiId = computed(() => route.query.poiId as string)
  const isEdit = computed(() => !!route.query.isEdit)

  // 从 store 获取编辑菜品数据
  const editDishData = computed(() => store.getters['menuManage/dish'] as IDishList | null)

  const menuList = computed(() => store.getters['menuManage/menuList'])

  const allDishList = computed(() => menuList.value.flatMap(menu => menu.items))

  interface FormData {
    dishId?: string
    dishName: string
    priceItem: {
      dishPrice: string
      priceType: number
    }
    dishResourceList: any[]
    specialty: boolean
  }

  const formData = ref<FormData>({
    dishName: '',
    priceItem: {
      dishPrice: '',
      priceType: 0
    },
    dishResourceList: [],
    specialty: false
  })

  const originalFormData = ref<FormData | null>(null)
  const specialtyDishCount = com

  // 表单验证规则
  const rules = reactive({
    dishName: [
      {
        type: 'string',
        required: true,
        trigger: 'blur',
        message: '请输入菜品名称'
      },
      {
        type: 'string',
        trigger: 'blur',
        validator: (_rule: any, value: string) => value.length <= 15,
        message: '菜品名称不能超过15个字符'
      }
    ],
    dishPrice: [
      {
        type: 'string',
        required: true,
        trigger: 'blur',
        message: '请输入菜品价格'
      },
      {
        type: 'string',
        trigger: 'blur',
        validator: (_rule: any, value: string) => /^\d+(\.\d{1})?$/.test(value),
        message: '价格格式不正确，最多保留1位小数'
      },
      {
        type: 'string',
        trigger: 'blur',
        validator: (_rule: any, value: string) => parseFloat(value) > 0,
        message: '价格必须大于0'
      }
    ],
    image: [
      {
        type: 'array',
        trigger: 'change',
        validator: (_rule: any, value: any[]) => value && value.length > 0,
        message: '请上传菜品图片'
      }
    ]
  })

  const onValidateDishName = (value: any) => {
    console.log('onValidateDishName', value)
  }

  // 是否可以设置为招牌菜
  const canSetSpecialty = computed(() => {
    if (formData.value.specialty) return true // 已经是招牌菜可以取消
    return specialtyDishCount.value < 10
  })

  const handleInfoClick = () => {
    alertMethodRef.value.showAlert({
      title: '招牌菜',
      message: '开启后将会同时在「招牌菜」分组展示',
      showCancelButton: false,
      showConfirmButton: true,
      confirmText: '我知道了',
    })
  }
  // 表单验证
  const isFormValid = computed(() => formData.value.dishName.trim() !== ''
    && formData.value.dishName.length <= 15
    && formData.value.priceItem.dishPrice !== ''
    && formData.value.dishResourceList.length > 0
    && parseFloat(formData.value.priceItem.dishPrice) > 0
    && /^\d+(\.\d{1})?$/.test(formData.value.priceItem.dishPrice))

  // 检测是否有变更 - 更精确的比较逻辑
  const hasChanges = computed(() => {
    if (!originalFormData.value) return false

    const current = formData.value
    const original = originalFormData.value

    // 比较菜品名称
    if (current.dishName !== original.dishName) return true

    // 比较价格信息
    if (current.priceItem.dishPrice !== original.priceItem.dishPrice) return true
    if (current.priceItem.priceType !== original.priceItem.priceType) return true

    // 比较招牌菜状态
    if (current.specialty !== original.specialty) return true

    // 比较图片列表 - 比较数量和文件ID
    if (current.dishResourceList.length !== original.dishResourceList.length) return true

    for (let i = 0; i < current.dishResourceList.length; i++) {
      const currentImg = current.dishResourceList[i]
      const originalImg = original.dishResourceList[i]

      // 比较文件ID或URL
      if (currentImg.fileId !== originalImg.fileId || currentImg.url !== originalImg.url) {
        return true
      }
    }

    return false
  })

  // 表单错误处理
  const onFormError = (errors: any, fields: any) => {
    console.error('表单验证失败:', errors, fields)
    // 显示第一个错误信息
    if (errors && errors.length > 0) {
      showToast(errors[0].message || '表单验证失败')
    }
  }

  // 招牌菜切换
  const onSpecialtyChange = (value: boolean) => {
    if (value && specialtyDishCount.value >= 10) {
      showToast('最多设置10个招牌菜')
      formData.value.specialty = false
      return
    }
    formData.value.specialty = value
  }

  // 初始化表单数据
  const initializeFormData = () => {
    if (editDishData.value) {
      // 使用传递的菜品数据
      const dish = editDishData.value
      formData.value = {
        ...formData.value,
        dishId: dish.dishId,
        dishName: dish.dishName,
        priceItem: {
          dishPrice: dish.priceItem.dishPrice.toString(),
          priceType: dish.priceItem.priceType,
        },
        dishResourceList: dish.dishResourceList?.map((item: any) => ({
          ...item,
          url: item.resourceUrl,
        })) || [],
        specialty: dish.specialty === SpecialtyType.YES
      }
      originalFormData.value = JSON.parse(JSON.stringify(formData.value))
    }
  }

  const checkDishDuplicate = async ({ dishName, dishPrice, priceType }: { dishName: string; dishPrice: number; priceType: number }) => {
    if (allDishList.value.length === 0) return false
    return allDishList.value.some(dish =>
      dish.dishName === dishName
      && dish.priceItem.dishPrice === dishPrice
      && dish.priceItem.priceType === priceType)
  }

  // 提交表单
  const onSubmit = async () => {
    // const result = await formRef.value.handleSubmit()
    // if (!result) return
    if (!isFormValid.value) return

    submitting.value = true

    // 菜品去重检查
    const isDuplicate = await checkDishDuplicate({
      dishName: formData.value.dishName,
      dishPrice: parseFloat(formData.value.priceItem.dishPrice),
      priceType: formData.value.priceItem.priceType
    })

    if (isDuplicate) {
      showToast('菜品重复')
      return
    }

    try {
      const dishData = {
        dishId: formData.value.dishId,
        dishName: formData.value.dishName,
        priceItem: {
          dishPrice: parseFloat(formData.value.priceItem.dishPrice),
          priceType: formData.value.priceItem.priceType
        },
        dishResourceList: formData.value.dishResourceList.map((item: any) => ({
          ...item,
          resourceId: item.fileId || '',
          type: 0,
          url: item.url,
          resourceInfo: 0,
        })) || [],
        specialty: formData.value.specialty ? 1 : 0, // 0-否，1-是
        dishSource: 0, // 0-商家上传
      }
      loading.value = true
      // 调用保存接口
      const res = await postUpsertMenuDish({
        poiId: poiId.value,
        menuGroup: {
          groupId: groupId.value,
          dish: dishData
        }
      })
      if (res.response?.success) {
        showToast(isEdit.value ? '菜品修改成功' : '菜品已添加')
        router.back()
      } else {
        showToast(res.response?.msg || '保存失败，请重试')
      }
    } catch (error: any) {
      console.error('保存失败:', error.message)
      showToast('保存失败，请重试')
    } finally {
      submitting.value = false
      loading.value = false
    }
  }

  // 显示退出确认弹窗
  const showExitConfirm = () => new Promise<boolean>(resolve => {
    alertMethodRef.value.showAlert({
      title: '提示',
      message: '当前编辑菜品内容尚未保存，确认退出吗？',
      confirmText: '确认退出',
      cancelText: '取消',
      showConfirmButton: true,
      showCancelButton: true,
      footerLayout: 'horizontal',
      footerType: 'button',
      onConfirm: () => {
        resolve(true)
      },
      onCancel: () => {
        resolve(false)
      }
    })
  })

  // 处理返回按钮点击
  const handleBackClick = async () => {
    const shouldLeave = await handleRouteLeave()
    if (shouldLeave) {
      invoke('canGoBack').then(res => {
        if (res.value) {
          router.back()
        } else {
          invoke('closeWindow').catch(() => {
            router.back()
          })
        }
      }).catch(() => {
        router.back()
      })
    }
  }

  // 处理路由离开
  const handleRouteLeave = async () => {
    // 如果没有变更，直接允许离开
    if (!hasChanges.value) {
      return true
    }

    // 显示确认弹窗
    const shouldLeave = await showExitConfirm()
    return shouldLeave
  }

  onMounted(() => {
    initializeFormData()
    invoke('disableCenterDragBack', { disable: false })
      .then(res => console.log(res))
      .catch(err => console.log(err))
  })

  onBeforeUnmount(() => {
    // 清理 store 中的编辑数据
    store.dispatch('menuManage/clearDish')
  })
</script>

<style lang="stylus" scoped>
.dish-create
  min-height 100vh
  background-color #f7f8fa

.form-container
  padding 16px 16px 120px

  .form-item
    background white
    border-radius 12px
    padding 16px
    margin-bottom 12px

.price-input-wrapper
  display flex
  align-items center
  gap 8px

.image-upload-container
  .dish-uploader
    margin-bottom 12px

  .upload-area
    width 80px
    height 80px
    border 2px dashed #dcdee0
    border-radius 8px
    display flex
    align-items center
    justify-content center
    background #fafafa

  .upload-tips
    font-size 12px
    color rgba(0, 0, 0, 0.45)
    line-height 18px
    margin-top 12px

.specialty-section
  display flex
  align-items center
  justify-content space-between

  .specialty-header
    display flex
    align-items center
    gap 4px

.footer-button
  position fixed
  bottom 0
  left 0
  right 0
  padding 16px
  background white
  border-top 1px solid #ebedf0
  z-index 100
.form-item-label-isSpecialty
  width 200px
  display flex
  align-items center
  gap 4px
  .form-item-label-isSpecialty-text
    font-size 16px
    color rgba(0, 0, 0, 0.8)
    font-weight 400
    line-height 24px
  .form-item-label-tips
    width 16px
    height 16px
</style>
